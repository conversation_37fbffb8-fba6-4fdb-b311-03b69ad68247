import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { AiOutlineEye } from "react-icons/ai";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { FiSearch } from "react-icons/fi";
import { useGetLastWeekOrdersWithDateRangeQuery } from "../../../store/api/pos/orderapi";

interface TipsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ApiTipRecord {
  _id: string;
  OrderNumber: string;
  operatorName: string;
  grandTotal: number;
  Tips: number;
  EmployeeName: string;
  createdAt: string;
}

const Tips: React.FC<TipsProps> = ({ isOpen, onClose }) => {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const userId = localStorage.getItem("userId") || "";

  // Build query params based on date selection
  const queryParams = {
    userId,
    ...(startDate && { startDate: startDate.toString() }),
    ...(endDate && { endDate: endDate.toString() })
  };

  // Fetch if we have userId and either no dates selected or both dates selected
  const shouldFetch = !!userId && ((!startDate && !endDate) || (!!startDate && !!endDate));

  const { data, isLoading, error } = useGetLastWeekOrdersWithDateRangeQuery(
    queryParams,
    { skip: !shouldFetch }
  );

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
      setCurrentPage(1); // Reset to first page when date changes
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Filter data based on search term
  const filteredData = data?.filter((tip: ApiTipRecord) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      tip.OrderNumber?.toLowerCase().includes(searchLower) ||
      tip.operatorName?.toLowerCase().includes(searchLower) ||
      tip.EmployeeName?.toLowerCase().includes(searchLower)
    );
  }) || [];

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer disabled:opacity-50"
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer disabled:opacity-50"
          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Tips"
      width="max-w-6xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search by Order Number, Operator or Employee"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || undefined,
                      endDate: endDate || undefined,
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">Error loading data</div>
          ) : paginatedData.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {(!startDate && !endDate)
                ? "No tips found"
                : (startDate && endDate)
                  ? "No tips found for the selected date range"
                  : "Please select both start and end dates to filter tips"}
            </div>
          ) : (
            <table className="w-full">
              <thead>
                <tr className="text-left border-b border-gray-200">
                  <th className="pb-3 text-gray-500 font-normal">
                    Order Number
                  </th>
                  <th className="pb-3 text-gray-500 font-normal">
                    Operator Name
                  </th>
                  <th className="pb-3 text-gray-500 font-normal">Amount</th>
                  <th className="pb-3 text-gray-500 font-normal">Tip</th>
                  <th className="pb-3 text-gray-500 font-normal">Employee</th>
                  <th className="pb-3 text-gray-500 font-normal">Date</th>
                  <th className="pb-3 text-gray-500 font-normal">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.map((tip: ApiTipRecord) => (
                  <tr key={tip._id} className="border-b border-gray-200">
                    <td className="py-4">{tip.OrderNumber}</td>
                    <td className="py-4">{tip.operatorName}</td>
                    <td className="py-4">${tip.grandTotal.toFixed(2)}</td>
                    <td className="py-4">${tip.Tips.toFixed(2)}</td>
                    <td className="py-4">{tip.EmployeeName || "N/A"}</td>
                    <td className="py-4">
                      {format(new Date(tip.createdAt), "MMM dd, yyyy")}
                    </td>
                    <td className="py-4">
                      <div className="relative group">
                        <AiOutlineEye
                          className="text-orange-500 cursor-pointer"
                          size={20}
                        />
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none">
                          View Tip
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </CustomModal>
  );
};

export default Tips;